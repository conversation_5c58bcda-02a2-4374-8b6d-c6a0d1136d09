import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IventCreatorTypeEnum, IventViewTypeEnum } from 'src/entities';
import { IventCardItem } from 'src/models/ivent-card-item';
import { IventListItem } from 'src/models/ivent-list-item';

export class CreateIventReturn {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the newly created ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;
}

export class GetBannerByIventIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(IventCardItem) },
    description: 'Array of ivent card items for banner display',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivents in the banner',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetIventPageByIventIdReturn {
  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  locationId!: string;

  @ApiProperty({ type: 'string' })
  mapboxId!: string;

  @ApiProperty({ type: 'string' })
  locationName!: string;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  date!: string[];

  @ApiProperty({ type: 'string', nullable: true })
  description!: string | null;

  @ApiProperty({ type: 'string' })
  categoryTag!: string;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  tags!: string[];

  @ApiProperty({ type: 'string' })
  creatorId!: string;

  @ApiProperty({ type: 'string', enum: Object.values(IventCreatorTypeEnum) })
  creatorType!: IventCreatorTypeEnum;

  @ApiProperty({ type: 'string' })
  creatorName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  creatorImageUrl!: string | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  collabNames!: string[];

  @ApiProperty({ type: 'number' })
  collabCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
  })
  memberNames!: string[] | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  memberAvatarUrls!: (string | null)[];

  @ApiProperty({ type: 'number' })
  memberCount!: number;

  @ApiProperty({ type: 'boolean', nullable: true })
  isFavorited!: boolean | null;

  @ApiProperty({ type: 'number', nullable: true })
  favoriteCount!: number | null;

  @ApiProperty({ type: 'string', nullable: true })
  googleFormsUrl!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  instagramUsername!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  whatsappUrl!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  whatsappNumber!: string | null;

  @ApiProperty({ type: 'boolean', nullable: true })
  isWhatsappUrlPrivate!: boolean | null;

  @ApiProperty({ type: 'string', nullable: true })
  callNumber!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  websiteUrl!: string | null;

  @ApiProperty({ type: 'string', enum: Object.values(IventViewTypeEnum) })
  viewType!: IventViewTypeEnum;
}

export class GetLatestIventsReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(IventListItem) },
        {
          type: 'object',
          properties: { squadId: { type: 'string' } },
        },
      ],
    },
  })
  ivents!: (IventListItem & { squadId: string | null })[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}

export class GetSuggestedImagesReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        imageName: { type: 'string' },
        imageUrl: { type: 'string' },
      },
    },
  })
  images!: {
    imageName: string;
    imageUrl: string;
  }[];

  @ApiProperty({ type: 'number' })
  imageCount!: number;
}

export class GetUpcomingIventReturn {
  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string' })
  date!: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  memberNames!: string[];

  @ApiProperty({ type: 'number' })
  memberCount!: number;
}
