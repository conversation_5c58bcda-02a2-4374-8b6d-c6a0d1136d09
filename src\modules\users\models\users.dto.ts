import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsString, IsUUID, IsUrl, Length, Matches } from 'class-validator';
import { UserEduVerificationEnum } from 'src/entities';

export class RegisterDto {
  @ApiProperty({
    type: 'string',
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
    required: true,
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumber!: string;

  @ApiProperty({
    type: 'string',
    description: "User's full name can only contain letters and spaces",
    example: '<PERSON> Doe',
    required: true,
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @Length(2, 50, { message: 'Full name must be between 2 and 50 characters' })
  @Matches(/^[a-zA-Z ]*$/, {
    message: 'Full name can only contain letters and spaces',
  })
  fullname!: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: 'Array of hobby UUIDs that the user is interested in',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby ID must be a valid UUID v4' })
  hobbyIds!: string[];
}

export class GetContactsByUserIdDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    example: ['+90(500)4003020'],
    description: 'Array of phone numbers to check for contacts',
    required: true,
  })
  @IsArray()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    each: true,
    message: 'Each phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumbers!: string[];
}

export class RemoveFollowerByUserIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the follower to remove',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'followerId must be a valid UUID v4' })
  followerId!: string;
}

export class SavePhoneContactsDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', example: '+90(500)4003020' },
    description: 'Array of phone numbers to save as contacts',
    required: true,
  })
  @IsArray()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    each: true,
    message: 'Each phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumbers!: string[];
}

export class SendCreatorRequestFormDto {}

export class SendVerificationEmailDto {}

export class UpdateByUserIdDto {
  @ApiProperty({
    type: 'string',
    description: 'Username can only contain letters, numbers, underscores, and hyphens',
    example: 'john_doe123',
    required: true,
    minLength: 4,
    maxLength: 20,
  })
  @IsString()
  @Length(4, 20, { message: 'Username must be between 4 and 20 characters' })
  @Matches(/^[a-zA-Z0-9_\-]{4,20}$/, {
    message: 'Username can only contain letters, numbers, underscores, and hyphens',
  })
  newUsername!: string;

  @ApiProperty({
    type: 'string',
    description: 'Birthday in YYYY-MM-DD format',
    example: '1990-01-15',
    required: true,
    format: 'date',
  })
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Birthday must be in YYYY-MM-DD format',
  })
  newBirthday!: string;

  @ApiProperty({
    type: 'string',
    description: 'User gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: true,
  })
  @IsString()
  @IsEnum(['male', 'female', 'other'], {
    message: 'Gender must be one of: male, female, other',
  })
  newGender!: string;

  @ApiProperty({
    type: 'string',
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    required: true,
    format: 'url',
  })
  @IsString()
  @IsUrl({}, { message: 'Avatar URL must be a valid URL' })
  newAvatarUrl!: string;
}

export class UpdateEmailByUserIdDto {
  @ApiProperty({
    type: 'string',
    description: 'New email address for the user',
    example: '<EMAIL>',
    required: true,
    format: 'email',
  })
  @IsString()
  @Matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, {
    message: 'Please provide a valid email address',
  })
  newEmail!: string;
}

export class UpdateGradByUserIdDto {
  @ApiProperty({
    type: 'string',
    enum: Object.values(UserEduVerificationEnum),
    description: 'User education verification status',
    example: UserEduVerificationEnum.STUDENT,
    required: true,
  })
  @IsEnum(UserEduVerificationEnum, {
    message: 'Graduation status must be a valid UserEduVerificationEnum value',
  })
  newGrad!: UserEduVerificationEnum;
}

export class UpdateNotificationsByUserIdDto {}

export class UpdatePhoneNumberByUserIdDto {
  @ApiProperty({
    type: 'string',
    example: '+90(500)4003020',
    description: 'New phone number in international format with country code',
    required: true,
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  newPhoneNumber!: string;
}

export class UpdatePrivacyByUserIdDto {}

export class ValidateEmailDto {}
