import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';

export class SendVerificationCodeDto {
  @ApiProperty({
    type: 'string',
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
    required: true,
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumber!: string;
}

export class ValidateDto {
  @ApiProperty({
    type: 'string',
    example: '123456',
    description: 'Six-digit verification code sent to the phone number',
    required: true,
    minLength: 6,
    maxLength: 6,
  })
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'Validation code must be exactly 6 digits',
  })
  validationCode!: string;

  @ApiProperty({
    type: 'string',
    example: '+90(500)4003020',
    description: 'Phone number in international format with country code',
    required: true,
  })
  @IsString()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  phoneNumber!: string;
}
